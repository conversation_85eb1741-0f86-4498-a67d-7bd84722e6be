// weekly-schedule.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { WeeklySession } from './weeklySession.entity';

export enum DayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

@Entity()
export class WeeklySchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.weeklySchedules, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @Column({ type: 'enum', enum: DayOfWeek, default: null })
  dayOfWeek: DayOfWeek;

  @OneToMany(() => WeeklySession, (session) => session.weeklySchedule, {
    cascade: true,
  })
  sessions: WeeklySession[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
